# This file was autogenerated by uv via the following command:
#    uv export --format requirements-txt --no-hashes
albucore==0.0.24
    # via albumentations
albumentations==2.0.8
    # via insightface
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   starlette
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
colorama==0.4.6 ; sys_platform == 'win32'
    # via
    #   click
    #   loguru
    #   pytest
    #   tqdm
coloredlogs==15.0.1
    # via onnxruntime
contourpy==1.3.2
    # via matplotlib
cycler==0.12.1
    # via matplotlib
cython==3.1.1
    # via insightface
easydict==1.13
    # via insightface
fastapi==0.115.12
    # via face-validation
flatbuffers==25.2.10
    # via onnxruntime
fonttools==4.58.1
    # via matplotlib
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via face-validation
httpx==0.28.1
    # via face-validation
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
imageio==2.37.0
    # via scikit-image
iniconfig==2.1.0
    # via pytest
insightface==0.7.3
    # via face-validation
joblib==1.5.1
    # via scikit-learn
kiwisolver==1.4.8
    # via matplotlib
lazy-loader==0.4
    # via scikit-image
loguru==0.7.3
    # via face-validation
matplotlib==3.10.3
    # via insightface
mpmath==1.3.0
    # via sympy
networkx==3.5
    # via scikit-image
numpy==2.2.6
    # via
    #   albucore
    #   albumentations
    #   contourpy
    #   imageio
    #   insightface
    #   matplotlib
    #   onnx
    #   onnxruntime
    #   opencv-python
    #   opencv-python-headless
    #   scikit-image
    #   scikit-learn
    #   scipy
    #   tifffile
onnx==1.18.0
    # via insightface
onnxruntime==1.22.0
    # via face-validation
opencv-python==*********
    # via face-validation
opencv-python-headless==*********
    # via
    #   albucore
    #   albumentations
packaging==25.0
    # via
    #   lazy-loader
    #   matplotlib
    #   onnxruntime
    #   pytest
    #   scikit-image
pillow==11.2.1
    # via
    #   face-validation
    #   imageio
    #   insightface
    #   matplotlib
    #   scikit-image
pluggy==1.6.0
    # via pytest
prettytable==3.16.0
    # via insightface
protobuf==6.31.1
    # via
    #   onnx
    #   onnxruntime
pydantic==2.11.5
    # via
    #   albumentations
    #   fastapi
pydantic-core==2.33.2
    # via pydantic
pyparsing==3.2.3
    # via matplotlib
pyreadline3==3.5.4 ; sys_platform == 'win32'
    # via humanfriendly
pytest==8.3.5
    # via
    #   face-validation
    #   pytest-asyncio
pytest-asyncio==1.0.0
    # via face-validation
python-dateutil==2.9.0.post0
    # via matplotlib
python-dotenv==1.1.0
    # via face-validation
pyyaml==6.0.2
    # via albumentations
requests==2.32.3
    # via insightface
scikit-image==0.25.2
    # via insightface
scikit-learn==1.6.1
    # via insightface
scipy==1.15.3
    # via
    #   albumentations
    #   insightface
    #   scikit-image
    #   scikit-learn
simsimd==6.2.1
    # via albucore
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
starlette==0.46.2
    # via fastapi
stringzilla==3.12.5
    # via albucore
sympy==1.14.0
    # via onnxruntime
threadpoolctl==3.6.0
    # via scikit-learn
tifffile==2025.5.26
    # via scikit-image
tqdm==4.67.1
    # via insightface
typing-extensions==4.13.2
    # via
    #   fastapi
    #   onnx
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via face-validation
uvloop==0.21.0
    # via face-validation
wcwidth==0.2.13
    # via prettytable
win32-setctime==1.2.0 ; sys_platform == 'win32'
    # via loguru
